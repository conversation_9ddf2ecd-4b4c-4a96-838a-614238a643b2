// masterparts.js - Enhanced parts database interface with Acumatica image integration
import { NotificationSystem } from "../core/notifications.js";
import { connectionManager } from "../core/connection.js";
import { partImageHelper } from "./part-image-helper.js";
import { InventorySyncComponent } from "./inventory-sync.js";
import { VendorMetricsComponent } from "./vendor-metrics.js";
import { PurchaseOrderBarcodeComponent } from "./purchase-order-barcode.js";

// IndexedDB helper for custom parts
class CustomPartsDB {
  constructor() {
    this.dbName = 'MasterPartsDB';
    this.version = 1;
    this.db = null;
  }

  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        try {
          const db = event.target.result;

          if (!db.objectStoreNames.contains('customParts')) {
            const store = db.createObjectStore('customParts', { keyPath: 'partNumber' });
            store.createIndex('description', 'description', { unique: false });
            store.createIndex('manufacturer', 'manufacturer', { unique: false });
          }

          if (!db.objectStoreNames.contains('partImages')) {
            db.createObjectStore('partImages', { keyPath: 'partNumber' });
          }
        } catch (error) {
          console.error('Error during database upgrade:', error);
          // Don't throw here to prevent transaction abort
        }
      };
    });
  }

  async savePart(partData) {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(['customParts'], 'readwrite');
        const store = transaction.objectStore('customParts');
        const request = store.put(partData);

        transaction.onerror = () => reject(transaction.error);
        transaction.onabort = () => reject(new Error('Transaction aborted'));

        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  async savePartImage(partNumber, imageData) {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(['partImages'], 'readwrite');
        const store = transaction.objectStore('partImages');
        const request = store.put({ partNumber, imageData });

        transaction.onerror = () => reject(transaction.error);
        transaction.onabort = () => reject(new Error('Transaction aborted'));

        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  async getAllParts() {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(['customParts'], 'readonly');
        const store = transaction.objectStore('customParts');
        const request = store.getAll();

        transaction.onerror = () => reject(transaction.error);
        transaction.onabort = () => reject(new Error('Transaction aborted'));

        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(request.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  async getPartImage(partNumber) {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(['partImages'], 'readonly');
        const store = transaction.objectStore('partImages');
        const request = store.get(partNumber);

        transaction.onerror = () => reject(transaction.error);
        transaction.onabort = () => reject(new Error('Transaction aborted'));

        request.onsuccess = () => resolve(request.result?.imageData || null);
        request.onerror = () => reject(request.error);
      } catch (error) {
        reject(error);
      }
    });
  }

  async deletePart(partNumber) {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(['customParts', 'partImages'], 'readwrite');

        transaction.onerror = () => reject(transaction.error);
        transaction.onabort = () => reject(new Error('Transaction aborted'));

        Promise.all([
          new Promise((resolve, reject) => {
            const request = transaction.objectStore('customParts').delete(partNumber);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
          }),
          new Promise((resolve, reject) => {
            const request = transaction.objectStore('partImages').delete(partNumber);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
          })
        ]).then(resolve).catch(reject);
      } catch (error) {
        reject(error);
      }
    });
  }
}

export class MasterPartsComponent {
  constructor(container) {
    this.container = container;
    this.partsData = [];
    this.customPartsDB = new CustomPartsDB();
    this.customParts = [];
    this.filteredParts = [];
    this.notificationSystem = new NotificationSystem();
    this.currentPage = 1;
    this.itemsPerPage = 10; // Increased for better use of 600px height
    this.currentSort = { field: 'Part Number', direction: 'asc' };
    this.searchTerm = '';
    this.selectedFilter = 'all';
    this.isDataLoaded = false;
    this.isLoading = false;
    
    // Track current part being displayed
    this.currentDetailPartId = null;
    
    // Selected parts list (bucket)
    this.selectedParts = [];
    
    // Tab system
    this.activeTab = 'master-parts';
    this.inventorySyncComponent = new InventorySyncComponent(container);
    this.vendorMetricsComponent = new VendorMetricsComponent(container);
    this.purchaseOrderBarcodeComponent = new PurchaseOrderBarcodeComponent(container);
    
    // Export settings
    this.exportSettings = {
      includeBasic: true,
      includeAdditional: true,
      includeImage: true,
      includeManufacturer: true,
      includeOrigin: true,
      includeHsCode: true,
      includeEccn: true,
      includeQuantity: true,
      includePrice: true
    };
    
    // Debounce function for search
    this.debounce = (func, delay) => {
      let debounceTimer;
      return function(...args) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => func.apply(this, args), delay);
      };
    };
    
    // Initialize from storage if available
    this.loadSelectedPartsFromStorage();
    this.loadExportSettingsFromStorage();
    this.init();
  }

  async init() {
    try {
      this.isLoading = true;
      this.render();
      
      // Initialize IndexedDB and load custom parts
      await this.initializeCustomPartsDB();
      
      // If we're in the master-parts tab, load parts data
      if (this.activeTab === 'master-parts') {
        await this.loadPartsData();
        await this.loadCustomParts();
      }
      
      this.isLoading = false;
      this.render();
      
      // Set up event listeners based on active tab
      if (this.activeTab === 'master-parts') {
        this.setupEventListeners();
        this.applySearch();
        this.updateSelectedPartsCounter();
      } else {
        // Just set up the tab functionality for other tabs
        this.setupTabFunctionality();
      }
      
      // Check for cookie permissions
      this.showCookiePermissionsMessage();
    } catch (error) {
      console.error('Error initializing MasterParts component:', error);
      this.showError(`Failed to initialize: ${error.message}`);
      this.isLoading = false;
    }
  }

  // Initialize custom parts database
  async initializeCustomPartsDB() {
    try {
      await this.customPartsDB.init();
      // Set reference in part image helper
      partImageHelper.setCustomPartsDB(this.customPartsDB);
      console.log('Custom parts database initialized');
    } catch (error) {
      console.error('Error initializing custom parts database:', error);
      this.addNotification('Failed to initialize custom parts database', 'error');
    }
  }

  // Load custom parts from IndexedDB
  async loadCustomParts() {
    try {
      this.customParts = await this.customPartsDB.getAllParts();
      console.log(`Loaded ${this.customParts.length} custom parts from IndexedDB`);
      
      // Mark custom parts as custom for identification and ensure field compatibility
      this.customParts.forEach(part => {
        part._isCustomPart = true;

        // Ensure backward compatibility - if old format, update to new format
        if (!part.partNumber && part['Part Number']) {
          part.partNumber = part['Part Number'];
        }
        if (!part.description && part['Description']) {
          part.description = part['Description'];
        }
        if (!part.manufacturer && part['Manufacturer']) {
          part.manufacturer = part['Manufacturer'];
        }

        // Ensure display fields exist
        if (!part['Part Number'] && part.partNumber) {
          part['Part Number'] = part.partNumber;
        }
        if (!part['Description'] && part.description) {
          part['Description'] = part.description;
        }
        if (!part['Manufacturer'] && part.manufacturer) {
          part['Manufacturer'] = part.manufacturer;
        }
      });
      
      // Merge custom parts with existing parts data
      this.mergeCustomParts();
    } catch (error) {
      console.error('Error loading custom parts:', error);
      this.addNotification('Failed to load custom parts', 'error');
    }
  }

  // Merge custom parts with existing parts data
  mergeCustomParts() {
    // Remove any existing custom parts from partsData to avoid duplicates
    this.partsData = this.partsData.filter(part => !part._isCustomPart);
    
    // Add custom parts to the beginning of the array
    this.partsData = [...this.customParts, ...this.partsData];
    
    // Update filtered parts
    this.filteredParts = [...this.partsData];
    
    console.log(`Total parts after merging: ${this.partsData.length}`);
  }
  
  // Load selected parts from storage
  loadSelectedPartsFromStorage() {
    if (chrome?.storage?.local) {
      chrome.storage.local.get('selectedParts', (result) => {
        if (result && result.selectedParts) {
          this.selectedParts = result.selectedParts;
          this.updateSelectedPartsCounter();
        }
      });
    }
  }
  
  // Load export settings from storage
  loadExportSettingsFromStorage() {
    if (chrome?.storage?.local) {
      chrome.storage.local.get('exportSettings', (result) => {
        if (result && result.exportSettings) {
          this.exportSettings = result.exportSettings;
          console.log('Loaded export settings from storage:', this.exportSettings);
        }
      });
    }
  }
  
  // Save export settings to storage
  saveExportSettingsToStorage() {
    if (chrome?.storage?.local) {
      chrome.storage.local.set({ 'exportSettings': this.exportSettings }, () => {
        console.log('Export settings saved to storage');
      });
    }
  }
  
  // Save selected parts to storage
  saveSelectedPartsToStorage() {
    if (chrome?.storage?.local) {
      chrome.storage.local.set({ 'selectedParts': this.selectedParts }, () => {
        console.log('Selected parts saved to storage');
      });
    }
  }
  
  // Add part to bucket
  addPartToBucket(partId) {
    const part = this.partsData.find(p => p['Part Number'] === partId);
    if (!part) return;
    
    // Check if already in selection
    if (!this.selectedParts.some(p => p['Part Number'] === partId)) {
      this.selectedParts.push({...part, quantity: 1});
      this.saveSelectedPartsToStorage();
      this.updateSelectedPartsCounter();
      this.addNotification(`Part ${partId} added to bucket`, "success");
      
      // Update UI to show the part is selected
      const rows = this.container.querySelectorAll(`tr[data-part-id="${partId}"]`);
      rows.forEach(row => {
        row.classList.add('bg-blue-50', 'dark:bg-blue-900', 'border-l-4', 'border-blue-500');
        
        // Find the add button and replace it with remove button
        const addButton = row.querySelector(`.add-selection-btn[data-part-id="${partId}"]`);
        if (addButton) {
          // Create the new remove button element
          const removeButton = document.createElement('button');
          removeButton.className = 'remove-selection-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-1';
          removeButton.setAttribute('data-part-id', partId);
          removeButton.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          `;
          
          // Add event listener to the new remove button
          removeButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation(); // Prevent row click
            this.removePartFromBucket(partId);
          });
          
          // Replace the old button with the new one
          addButton.parentNode.replaceChild(removeButton, addButton);
        }
      });
      
      // Also update the add-to-bucket button in the part details modal if open
      if (this.currentDetailPartId === partId) {
        const addToBucketBtn = this.container.querySelector('#addToBucketBtn');
        if (addToBucketBtn) {
          addToBucketBtn.innerHTML = `
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Remove from Bucket
          `;
        }
      }
    } else {
      this.addNotification(`Part ${partId} is already in your bucket`, "info");
    }
  }
  
  // Remove part from bucket
  removePartFromBucket(partId) {
    const index = this.selectedParts.findIndex(p => p['Part Number'] === partId);
    if (index !== -1) {
      this.selectedParts.splice(index, 1);
      this.saveSelectedPartsToStorage();
      this.updateSelectedPartsCounter();
      this.addNotification(`Part ${partId} removed from bucket`, "info");
      
      // Update UI
      const rows = this.container.querySelectorAll(`tr[data-part-id="${partId}"]`);
      rows.forEach(row => {
        row.classList.remove('bg-blue-50', 'dark:bg-blue-900', 'border-l-4', 'border-blue-500');
        
        // Find the remove button and replace with add button
        const removeButton = row.querySelector(`.remove-selection-btn[data-part-id="${partId}"]`);
        if (removeButton) {
          // Create the new add button element
          const addButton = document.createElement('button');
          addButton.className = 'add-selection-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 p-1';
          addButton.setAttribute('data-part-id', partId);
          addButton.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          `;
          
          // Add event listener to the new add button
          addButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation(); // Prevent row click
            this.addPartToBucket(partId);
          });
          
          // Replace the old button with the new one
          removeButton.parentNode.replaceChild(addButton, removeButton);
        }
      });
      
      // Also update the add-to-bucket button in the part details modal if open
      if (this.currentDetailPartId === partId) {
        const addToBucketBtn = this.container.querySelector('#addToBucketBtn');
        if (addToBucketBtn) {
          addToBucketBtn.innerHTML = `
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Add to Bucket
          `;
        }
      }
    }
  }
  
  // Update quantity of selected part
  updatePartQuantity(partId, quantity) {
    const part = this.selectedParts.find(p => p['Part Number'] === partId);
    if (part) {
      part.quantity = Math.max(1, parseInt(quantity) || 1);
      this.saveSelectedPartsToStorage();
    }
  }
  
  // Update the counter showing number of selected parts
  updateSelectedPartsCounter() {
    const badge = this.container.querySelector('#selectedPartsBadge');
    if (badge) {
      // First, update the counter if it exists
      const counter = this.container.querySelector('#selectedPartsCounter');
      
      if (this.selectedParts.length > 0) {
        // Show the counter badge
        if (counter) {
          counter.textContent = this.selectedParts.length;
        } else {
          // If counter doesn't exist, create it
          const counterBadge = document.createElement('span');
          counterBadge.id = 'selectedPartsCounter';
          counterBadge.className = 'absolute -top-2 -right-2 bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs';
          counterBadge.textContent = this.selectedParts.length;
          badge.appendChild(counterBadge);
        }
      } else {
        // Remove the counter if it exists
        if (counter) counter.remove();
      }
      
      // Always keep text color consistent
      badge.className = badge.className.replace(/text-blue-600|dark:text-blue-400/g, '');
    }
  }
  
  // Load and display part image
  async loadPartImage(partId, container) {
    // Check if container exists
    if (!container) {
      console.warn("Image container not found for part", partId);
      return;
    }
    
    // Define no-thumbnail image
    const noThumbnailImage = chrome.runtime.getURL('images/extra/no-thumbnail.png');
    
    // Check if this is a custom part
    const part = this.partsData.find(p => p['Part Number'] === partId);
    const isCustomPart = part && part._isCustomPart;
    
    if (isCustomPart) {
      // Handle custom part image from IndexedDB
      container.innerHTML = `
        <div class="text-center py-4 h-full w-full flex flex-col items-center justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Loading image...</p>
        </div>
      `;
      
      try {
        const imageData = await this.customPartsDB.getPartImage(partId);
        if (imageData) {
          container.innerHTML = `
            <div class="text-center h-full w-full flex flex-col items-center justify-center">
              <img src="${imageData}" alt="Part ${partId}" class="max-h-28 max-w-full object-contain border border-gray-200 dark:border-gray-700 rounded">
              <p class="text-xs text-gray-500 dark:text-gray-400 truncate mt-2">Custom part image</p>
            </div>
          `;
        } else {
          container.innerHTML = `
            <div class="text-center h-full w-full flex flex-col items-center justify-center">
              <img src="${noThumbnailImage}" alt="No image available" class="max-h-28 max-w-full object-contain">
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">No image available</p>
            </div>
          `;
        }
      } catch (error) {
        console.error('Error loading custom part image:', error);
        container.innerHTML = `
          <div class="text-center h-full w-full flex flex-col items-center justify-center">
            <img src="${noThumbnailImage}" alt="No image available" class="max-h-28 max-w-full object-contain">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Error loading image</p>
          </div>
        `;
      }
      return;
    }
    
    // Handle Acumatica parts
    // Check if connected to Acumatica
    const connectionStatus = connectionManager.getConnectionStatus();
    const isConnected = connectionStatus.acumatica.isConnected;
    
    if (!isConnected) {
      container.innerHTML = `
        <div class="text-center h-full w-full flex flex-col items-center justify-center">
          <img src="${noThumbnailImage}" alt="No image available" class="max-h-28 max-w-full object-contain">
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Not connected to Acumatica</p>
        </div>
      `;
      return;
    }
    
    // Show loading indicator
    container.innerHTML = `
      <div class="text-center py-4 h-full w-full flex flex-col items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Loading image...</p>
      </div>
    `;
    
    try {
      // Fetch image data with a timeout to prevent hanging UI
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error("Image request timed out")), 10000)
      );
      
      const imageResultPromise = partImageHelper.getPartImage(partId);
      const imageResult = await Promise.race([imageResultPromise, timeoutPromise]);
      
      if (imageResult.success) {
        container.innerHTML = `
          <div class="text-center h-full w-full flex flex-col items-center justify-center">
            <img src="${imageResult.imageUrl}" alt="Part ${partId}" class="max-h-28 max-w-full object-contain border border-gray-200 dark:border-gray-700 rounded">
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate mt-2">${imageResult.fileName || 'Part image'}</p>
          </div>
        `;
      } else {
        // Always show the generic "No image available" message regardless of error type
        container.innerHTML = `
          <div class="text-center h-full w-full flex flex-col items-center justify-center">
            <img src="${noThumbnailImage}" alt="No image available" class="max-h-28 max-w-full object-contain">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">No image available</p>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error loading part image:', error);
      if (container) {
        container.innerHTML = `
          <div class="text-center h-full w-full flex flex-col items-center justify-center">
            <img src="${noThumbnailImage}" alt="No image available" class="max-h-28 max-w-full object-contain">
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Error loading image</p>
          </div>
        `;
      }
    }
  }
  
  // Show the selected parts list (bucket)
  async showBucketList() {
    // Remove any existing modal first
    const existingModal = this.container.querySelector('#selectedPartsModal');
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal for selected parts
    const modalHtml = `
      <div id="selectedPartsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-11/12 max-h-[540px] overflow-hidden flex flex-col">
          <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Parts Bucket (${this.selectedParts.length})</h3>
            <button id="closeSelectedPartsModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="mb-4 overflow-y-auto" style="max-height: 340px;">
            ${this.selectedParts.length === 0 ? 
              `<div class="text-center py-8 text-gray-500">
                <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <p class="text-lg">No parts in bucket</p>
                <p class="text-sm mt-2">Add parts from the database to your bucket.</p>
              </div>` 
              :
              `<div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
                  <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
                    <tr>
                      <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Part #</th>
                      <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                      <th scope="col" class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                      <th scope="col" class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                      <th scope="col" class="px-2 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Price</th>
                      <th scope="col" class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Image</th>
                      <th scope="col" class="px-2 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800">
                    ${this.selectedParts.map((part, idx) => `
                      <tr class="${idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'}">
                        <td class="px-2 py-1 whitespace-nowrap">
                          <div class="text-xs font-medium text-blue-600 dark:text-blue-400">${part['Part Number'] || '-'}</div>
                        </td>
                        <td class="px-2 py-1">
                          <div class="text-xs text-gray-900 dark:text-gray-300 truncate" style="max-width: 200px;" title="${part['Description'] || '-'}">${part['Description'] || '-'}</div>
                        </td>
                        <td class="px-2 py-1 text-center">
                          <span class="px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${this.getStockStatusClass(part['Stock Status'])}">${part['Stock Status'] || 'Unknown'}</span>
                        </td>
                        <td class="px-2 py-1 text-center">
                          <input type="number" min="1" value="${part.quantity || 1}" 
                            class="w-12 px-1 py-1 text-xs text-center border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                            data-part-id="${part['Part Number']}"
                            onchange="document.dispatchEvent(new CustomEvent('quantityChanged', {detail: {partId: '${part['Part Number']}', quantity: this.value}}))"
                          >
                        </td>
                        <td class="px-2 py-1 text-right">
                          <div class="text-xs text-gray-900 dark:text-gray-300">${part['Default Price'] ? `$${part['Default Price']}` : '-'}</div>
                        </td>
                        <td class="px-2 py-1 text-center">
                          <div id="bucket-img-${part['Part Number']}" class="h-10 flex items-center justify-center">
                            <div class="animate-pulse h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          </div>
                        </td>
                        <td class="px-2 py-1 text-right">
                          <button class="remove-selected-part-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-1" data-part-id="${part['Part Number']}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        </td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>`
            }
          </div>
          
          <div class="flex justify-end mt-auto pt-3 border-t border-gray-200 dark:border-gray-700 space-x-3">
            ${this.selectedParts.length > 0 ? 
              `<button id="clearBucketBtn" class="px-3 py-1 text-white bg-red-600 rounded-md hover:bg-red-700 text-sm flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Clear Bucket
              </button>
              <button id="exportBucketHtmlBtn" class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Export
              </button>` 
              : ''
            }
            <button id="closeSelectionBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
              Close
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Append modal to container
    this.container.insertAdjacentHTML('beforeend', modalHtml);
    
    // Set up event listeners
    const closeButtons = this.container.querySelectorAll('#closeSelectedPartsModalBtn, #closeSelectionBtn');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const modal = this.container.querySelector('#selectedPartsModal');
        if (modal) modal.remove();
      });
    });
    
    // Set up remove buttons
    const removeButtons = this.container.querySelectorAll('.remove-selected-part-btn');
    removeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const partId = btn.getAttribute('data-part-id');
        this.removePartFromBucket(partId);
        
        // Refresh the modal
        this.showBucketList();
      });
    });
    
    // Set up clear bucket button
    const clearBtn = this.container.querySelector('#clearBucketBtn');
    if (clearBtn) {
      clearBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (confirm('Are you sure you want to clear all parts from the bucket?')) {
          this.selectedParts = [];
          this.saveSelectedPartsToStorage();
          this.updateSelectedPartsCounter();
          
          // Refresh the table to remove highlighting
          this.updatePartsTable();
          
          // Close the modal
          const modal = this.container.querySelector('#selectedPartsModal');
          if (modal) modal.remove();
          
          this.addNotification('Bucket cleared', 'info');
        }
      });
    }
    
    // Set up export HTML button
    const exportHtmlBtn = this.container.querySelector('#exportBucketHtmlBtn');
    if (exportHtmlBtn) {
      exportHtmlBtn.addEventListener('click', (e) => {
        e.preventDefault();
        // Use global settings for bucket export
        this.exportPartsAsHtml(this.selectedParts, 'bucket', this.exportSettings);
      });
    }
    
    // Listen for quantity changes
    document.addEventListener('quantityChanged', (e) => {
      const { partId, quantity } = e.detail;
      this.updatePartQuantity(partId, quantity);
    });
    
    // Load images for parts in bucket
    if (this.selectedParts.length > 0) {
      // Define no-thumbnail image
      const noThumbnailImage = chrome.runtime.getURL('images/extra/no-thumbnail.png');
      
      // Process each part individually to handle custom vs Acumatica parts
      await Promise.all(this.selectedParts.map(async (part) => {
        const partId = part['Part Number'];
        const imgContainer = this.container.querySelector(`#bucket-img-${partId}`);
        
        if (!imgContainer) return;
        
        try {
          if (part._isCustomPart) {
            // Load custom part image from IndexedDB
            const imageData = await this.customPartsDB.getPartImage(partId);
            if (imageData) {
              imgContainer.innerHTML = `<img src="${imageData}" alt="Part ${partId}" class="h-8 w-auto mx-auto object-contain">`;
            } else {
              imgContainer.innerHTML = `<img src="${noThumbnailImage}" alt="No image" class="h-8 w-auto mx-auto object-contain opacity-50">`;
            }
          } else {
            // Load Acumatica part image
            const connectionStatus = connectionManager.getConnectionStatus();
            const isConnected = connectionStatus.acumatica.isConnected;
            
            if (isConnected) {
              const imageResult = await partImageHelper.getPartImage(partId);
              if (imageResult.success) {
                imgContainer.innerHTML = `<img src="${imageResult.imageUrl}" alt="Part ${partId}" class="h-8 w-auto mx-auto object-contain">`;
              } else {
                imgContainer.innerHTML = `<img src="${noThumbnailImage}" alt="No image" class="h-8 w-auto mx-auto object-contain opacity-50">`;
              }
            } else {
              imgContainer.innerHTML = `<img src="${noThumbnailImage}" alt="No image" class="h-8 w-auto mx-auto object-contain opacity-50">`;
            }
          }
        } catch (error) {
          console.error(`Error loading image for part ${partId}:`, error);
          imgContainer.innerHTML = `<img src="${noThumbnailImage}" alt="No image" class="h-8 w-auto mx-auto object-contain opacity-50">`;
        }
      }));
    }
  }
  
  // Export parts as HTML in a new tab
  async exportPartsAsHtml(parts, source = 'database', settings = null) {
    if (parts.length === 0) {
      this.addNotification('No parts to export', 'warning');
      return;
    }
    
    // Use global settings if none provided
    if (!settings) {
      settings = this.exportSettings;
    }
    
    try {
      // Show loading notification
      this.addNotification('Preparing export...', 'info');
      
      // Define no-thumbnail image for export
      const noThumbnailImage = chrome.runtime.getURL('images/extra/no-thumbnail.png');
      
      // Create HTML content
      let title = '';
      if (source === 'bucket') {
        title = 'Parts Bucket Export';
      } else if (source === 'detail') {
        title = `Part Details: ${parts[0]['Part Number']}`;
      } else {
        title = 'Parts Database Export';
      }
      
      // Get connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      const isConnected = connectionStatus.acumatica.isConnected;
      
      // Prepare image data
      const partIds = parts.map(part => part['Part Number']);
      let imageMap = new Map();
      
      if (settings.includeImage && isConnected) {
        // For detail view of a single part, use getPartImage for simplicity
        if (source === 'detail' && parts.length === 1) {
          const imageResult = await partImageHelper.getPartImage(parts[0]['Part Number']);
          if (imageResult.success) {
            // Convert to base64 for embedding in HTML
            const dataUrl = await partImageHelper.getImageAsDataUrl(imageResult.imageUrl);
            imageMap.set(parts[0]['Part Number'], dataUrl);
          }
        } 
        // For bucket or multiple parts, get all images at once
        else {
          this.addNotification('Fetching part images...', 'info');
          // Get all image URLs
          const imageResults = await partImageHelper.getMultiplePartImages(partIds);
          
          // Convert successful results to data URLs for embedding
          for (const [partId, result] of imageResults.entries()) {
            if (result.success) {
              // Convert to base64 for embedding in HTML
              const dataUrl = await partImageHelper.getImageAsDataUrl(result.imageUrl);
              imageMap.set(partId, dataUrl);
            }
          }
        }
      }
      
      let html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          h1 {
            color: #2563eb;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
          }
          .timestamp {
            color: #6b7280;
            font-size: 0.9em;
            margin-bottom: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #e5e7eb;
          }
          th {
            background-color: #f3f4f6;
            font-weight: bold;
          }
          tr:nth-child(even) {
            background-color: #f9fafb;
          }
          .part-details {
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            padding: 15px;
          }
          .part-header {
            font-size: 1.2em;
            color: #2563eb;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .part-image {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            padding: 5px;
            background-color: white;
          }
          .part-image-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
          }
          .detail-section {
            margin-bottom: 15px;
          }
          .detail-section h3 {
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
          }
          .status-Stock {
            background-color: #d1fae5;
            color: #065f46;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: bold;
          }
          .status-Low-Stock {
            background-color: #fef3c7;
            color: #92400e;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: bold;
          }
          .status-Out-of-Stock {
            background-color: #fee2e2;
            color: #b91c1c;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: bold;
          }
          .status-Non-Stock {
            background-color: #dbeafe;
            color: #1e40af;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: bold;
          }
          .status-Discontinued {
            background-color: #e5e7eb;
            color: #4b5563;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: bold;
          }
          .two-columns {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
          }
          @media print {
            body {
              font-size: 12px;
            }
            h1 {
              font-size: 18px;
            }
            .no-print {
              display: none;
            }
            .part-details {
              page-break-inside: avoid;
            }
            .part-image {
              max-width: 150px;
              max-height: 150px;
            }
          }
        </style>
      </head>
      <body>
        <h1>${title}</h1>
        <div class="timestamp">Generated: ${new Date().toLocaleString()}</div>
      `;

      // Add summary table (without price column for bucket export)
      html += `
        <table>
          <thead>
            <tr>
              <th>Part #</th>
              <th>Description</th>
              ${settings.includeManufacturer ? '<th>Manufacturer</th>' : ''}
              <th>Status</th>
              ${(source === 'bucket' && settings.includeQuantity) ? '<th>Quantity</th>' : ''}
              ${settings.includePrice ? '<th>Price</th>' : ''}
              ${settings.includeImage ? '<th>Image</th>' : ''}
            </tr>
          </thead>
          <tbody>
      `;

      parts.forEach(part => {
        const statusClass = part['Stock Status'] ? 
          `status-${part['Stock Status'].replace(/\s+/g, '-')}` : '';
        
        const hasImage = settings.includeImage && imageMap.has(part['Part Number']);
        const imageHtml = hasImage ? 
          `<img src="${imageMap.get(part['Part Number'])}" alt="Part ${part['Part Number']}" style="max-height: 60px; max-width: 60px;">` : 
          `<span style="color: #6b7280; font-style: italic;">No image</span>`;
        
        html += `
          <tr>
            <td>${part['Part Number'] || '-'}</td>
            <td>${part['Description'] || '-'}</td>
            ${settings.includeManufacturer ? `<td>${part['Manufacturer'] || '-'}</td>` : ''}
            <td><span class="${statusClass}">${part['Stock Status'] || 'Unknown'}</span></td>
            ${(source === 'bucket' && settings.includeQuantity) ? `<td>${part.quantity || 1}</td>` : ''}
            ${settings.includePrice ? `<td>${part['Default Price'] ? '$' + part['Default Price'] : '-'}</td>` : ''}
            ${settings.includeImage ? `<td style="text-align: center;">${imageHtml}</td>` : ''}
          </tr>
        `;
      });

      html += `
          </tbody>
        </table>
      `;

      // Add detailed information for each part
      // For bucket, always show the detailed info
      if ((parts.length <= 5 || source === 'detail' || source === 'bucket') && 
          (settings.includeBasic || settings.includeAdditional)) {
        parts.forEach(part => {
          const hasImage = settings.includeImage && imageMap.has(part['Part Number']);
          const imageHtml = hasImage ? 
            `<div class="part-image-container">
               <img src="${imageMap.get(part['Part Number'])}" alt="Part ${part['Part Number']}" class="part-image" style="max-height: 150px; object-fit: contain;">
             </div>` : 
            `<div class="part-image-container">
               <img src="${noThumbnailImage}" alt="No image available" class="part-image" style="max-height: 150px; object-fit: contain;">
             </div>`;
          
          html += `
            <div class="part-details">
              <div class="part-header">
                <span>Part Details: ${part['Part Number']}</span>
                ${(source === 'bucket' && settings.includeQuantity) ? `<span>Quantity: ${part.quantity || 1}</span>` : ''}
              </div>
              
              <div class="two-columns">
                ${settings.includeImage ? `
                <div>
                  ${imageHtml}
                </div>
                ` : ''}
                
                ${settings.includeBasic ? `
                <div>
                  <div class="detail-section">
                    <h3>Basic Information</h3>
                    <table>
                      <tbody>
                        <tr><td><strong>Part Number</strong></td><td>${part['Part Number'] || '-'}</td></tr>
                        <tr><td><strong>Description</strong></td><td>${part['Description'] || '-'}</td></tr>
                        ${settings.includeManufacturer ? `<tr><td><strong>Supplier</strong></td><td>${part['Supplier'] || '-'}</td></tr>` : ''}
                        ${settings.includeManufacturer ? `<tr><td><strong>Supplier P/N</strong></td><td>${part['Supplier P/N'] || '-'}</td></tr>` : ''}
                        ${settings.includeManufacturer ? `<tr><td><strong>Manufacturer</strong></td><td>${part['Manufacturer'] || '-'}</td></tr>` : ''}
                        ${settings.includeOrigin ? `<tr><td><strong>Country of Origin</strong></td><td>${part['Country of Origin'] || '-'}</td></tr>` : ''}
                        <tr><td><strong>UOM</strong></td><td>${part['UOM'] || '-'}</td></tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                ` : ''}
              </div>
              
              ${settings.includeAdditional ? `
              <div class="detail-section">
                <h3>Additional Details</h3>
                <table>
                  <tbody>
                    <tr><td><strong>Stock Status</strong></td><td><span class="${part['Stock Status'] ? `status-${part['Stock Status'].replace(/\s+/g, '-')}` : ''}">${part['Stock Status'] || 'Unknown'}</span></td></tr>
                    ${settings.includePrice ? `<tr><td><strong>Default Price</strong></td><td>${part['Default Price'] ? '$' + part['Default Price'] : '-'}</td></tr>` : ''}
                    <tr><td><strong>Weight</strong></td><td>${part['Weight'] || '-'}</td></tr>
                    ${settings.includeHsCode ? `<tr><td><strong>HS Code</strong></td><td>${part['HS Code'] || '-'}</td></tr>` : ''}
                    ${settings.includeEccn ? `<tr><td><strong>ECCN</strong></td><td>${part['ECCN'] || '-'}</td></tr>` : ''}
                    <tr><td><strong>USMCA/CASMA</strong></td><td>${part['USMCA/CASMA'] || '-'}</td></tr>
                    <tr><td><strong>Schedule B Code</strong></td><td>${part['Schedule B Code'] || '-'}</td></tr>
                  </tbody>
                </table>
              </div>
              
              <div class="detail-section">
                <h3>Additional Information</h3>
                <table>
                  <tbody>
                    ${settings.includeManufacturer ? `<tr><td><strong>Manufacturer & Address</strong></td><td>${part['Manufacturer & Address'] || '-'}</td></tr>` : ''}
                    <tr><td><strong>Customs Description</strong></td><td>${part['Customs Description'] || '-'}</td></tr>
                    <tr><td><strong>Preference Criteria</strong></td><td>${part['Preference Criteria'] || '-'}</td></tr>
                    <tr><td><strong>HazMat Classification</strong></td><td>${part['HazMat Classification'] || '-'}</td></tr>
                  </tbody>
                </table>
              </div>
              ` : ''}
            </div>
          `;
        });
      }

      html += `
        </body>
        </html>
      `;

      // Open a new tab and write the HTML
      const newTab = window.open('', '_blank');
      if (newTab) {
        newTab.document.write(html);
        newTab.document.close();
        this.addNotification(`Exported ${parts.length} part${parts.length > 1 ? 's' : ''} as HTML`, 'success');
      } else {
        this.addNotification('Could not open a new tab. Please check your popup blocker settings.', 'error');
      }
    } catch (error) {
      console.error('Error exporting parts as HTML:', error);
      this.addNotification(`Export failed: ${error.message}`, 'error');
    }
  }
  
  // Show Add Part Modal
  showAddPartModal() {
    const modal = this.container.querySelector('#addPartModal');
    if (modal) {
      modal.classList.remove('hidden');
      
      // Reset form
      const form = this.container.querySelector('#addPartForm');
      if (form) {
        form.reset();
        // Hide image preview and show upload area
        const preview = this.container.querySelector('#imagePreview');
        const uploadArea = this.container.querySelector('#imageUploadArea');
        if (preview) preview.classList.add('hidden');
        if (uploadArea) uploadArea.classList.remove('hidden');
      }
      
      // Set up event listeners for modal
      this.setupAddPartModalListeners();
    }
  }

  // Set up event listeners for Add Part Modal
  setupAddPartModalListeners() {
    // Close modal button
    const closeBtn = this.container.querySelector('#closeAddPartModalBtn');
    const cancelBtn = this.container.querySelector('#cancelAddPartBtn');
    
    [closeBtn, cancelBtn].forEach(btn => {
      if (btn) {
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          this.hideAddPartModal();
        });
      }
    });
    
    // Image preview functionality
    const imageInput = this.container.querySelector('#partImageInput');
    if (imageInput) {
      imageInput.addEventListener('change', (e) => {
        this.handleImagePreview(e.target.files[0]);
      });
    }
    
    // Form submission
    const form = this.container.querySelector('#addPartForm');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddPartSubmission(form);
      });
    }
    
    // Click outside modal to close
    const modal = this.container.querySelector('#addPartModal');
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideAddPartModal();
        }
      });
    }
  }

  // Hide Add Part Modal
  hideAddPartModal() {
    const modal = this.container.querySelector('#addPartModal');
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  // Handle image preview
  handleImagePreview(file) {
    if (!file) return;

    const preview = this.container.querySelector('#imagePreview');
    const previewImg = this.container.querySelector('#previewImg');
    const uploadArea = this.container.querySelector('#imageUploadArea');

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (previewImg) previewImg.src = e.target.result;
        if (preview) preview.classList.remove('hidden');
        if (uploadArea) uploadArea.classList.add('hidden');
      };
      reader.readAsDataURL(file);
    } else {
      this.addNotification('Please select a valid image file', 'error');
      if (preview) preview.classList.add('hidden');
      if (uploadArea) uploadArea.classList.remove('hidden');
    }
  }

  // Handle Add Part form submission
  async handleAddPartSubmission(form) {
    try {
      const formData = new FormData(form);
      
      // Validate required fields
      const partNumber = formData.get('partNumber').trim();
      const description = formData.get('description').trim();
      
      if (!partNumber || !description) {
        this.addNotification('Part Number and Description are required', 'error');
        return;
      }
      
      // Check if part number already exists
      const existingPart = this.partsData.find(p => p['Part Number'] === partNumber);
      if (existingPart) {
        this.addNotification(`Part Number ${partNumber} already exists`, 'error');
        return;
      }
      
      // Create part object
      const newPart = {
        partNumber: partNumber, // IndexedDB keyPath
        'Part Number': partNumber, // Display field
        description: description, // IndexedDB index field
        'Description': description, // Display field
        manufacturer: formData.get('manufacturer') || '', // IndexedDB index field
        'Supplier': formData.get('supplier') || '',
        'Supplier P/N': formData.get('supplierPN') || '',
        'Manufacturer': formData.get('manufacturer') || '',
        'Country of Origin': formData.get('countryOfOrigin') || '',
        'UOM': formData.get('uom') || '',
        'Stock Status': formData.get('stockStatus') || 'Stock',
        'Default Price': formData.get('defaultPrice') || '',
        'Weight': formData.get('weight') || '',
        'HS Code': formData.get('hsCode') || '',
        'ECCN': formData.get('eccn') || '',
        'USMCA/CASMA': formData.get('usmcaCasma') || '',
        'Schedule B Code': formData.get('scheduleBCode') || '',
        'Manufacturer & Address': formData.get('manufacturerAddress') || '',
        'Customs Description': formData.get('customsDescription') || '',
        'Preference Criteria': formData.get('preferenceCriteria') || '',
        'HazMat Classification': formData.get('hazmatClassification') || '',
        '_isCustomPart': true,
        '_dateAdded': new Date().toISOString()
      };
      
      // Handle image upload
      const imageFile = formData.get('partImage');
      let imageData = null;
      
      if (imageFile && imageFile.size > 0) {
        imageData = await this.fileToBase64(imageFile);
        await this.customPartsDB.savePartImage(partNumber, imageData);
      }
      
      // Save part to IndexedDB
      await this.customPartsDB.savePart(newPart);
      
      // Add to local arrays
      this.customParts.push(newPart);
      this.partsData.unshift(newPart); // Add to beginning
      this.filteredParts = [...this.partsData];
      
      // Update UI
      this.updatePartsTable();
      this.hideAddPartModal();
      
      this.addNotification(`Part ${partNumber} added successfully`, 'success');
      
    } catch (error) {
      console.error('Error adding part:', error);
      this.addNotification(`Failed to add part: ${error.message}`, 'error');
    }
  }

  // Convert file to base64
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  // Show Master Part settings dialog (formerly export settings)
  showMasterPartSettings() {
    // Remove any existing modal first
    const existingModal = this.container.querySelector('#masterPartSettingsModal');
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal for master part settings
    const modalHtml = `
      <div id="masterPartSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-96" style="max-height: 540px; overflow-y: auto;">
          <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Master Part Settings</h3>
            <button id="closeSettingsBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Actions Section -->
          <div class="mb-4">
            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-md mb-3">
              <h4 class="text-sm font-medium mb-2 text-gray-800 dark:text-white">Actions</h4>
              <div class="flex flex-col space-y-2">
                <button id="settingsRefreshBtn" class="px-2 py-2 text-sm font-medium rounded-md bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200 flex items-center justify-center hover:bg-blue-200 dark:hover:bg-blue-800">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Refresh Parts Database
                </button>
                
                <button id="settingsExportBtn" class="px-2 py-2 text-sm font-medium rounded-md bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200 flex items-center justify-center hover:bg-green-200 dark:hover:bg-green-800">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                  </svg>
                  Export Database
                </button>
              </div>
            </div>
            
            <!-- Export Settings Section -->
            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
              <h4 class="text-sm font-medium mb-2 text-gray-800 dark:text-white">Export Settings</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Configure what to include in your exports:</p>
              
              <div class="space-y-3">
                <!-- Content Sections -->
                <div class="ml-2 space-y-2">
                  <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Content Sections</h5>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="basic" ${this.exportSettings.includeBasic ? 'checked' : ''}>
                      <span class="text-sm">Basic Information</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="additional" ${this.exportSettings.includeAdditional ? 'checked' : ''}>
                      <span class="text-sm">Additional Details</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="image" ${this.exportSettings.includeImage ? 'checked' : ''}>
                      <span class="text-sm">Images</span>
                      <span class="ml-1 text-xs text-gray-500">(slower export)</span>
                    </label>
                  </div>
                </div>
                
                <!-- Field Options -->
                <div class="ml-2 space-y-2">
                  <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Fields to Include</h5>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="manufacturer" ${this.exportSettings.includeManufacturer ? 'checked' : ''}>
                      <span class="text-sm">Manufacturer</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="origin" ${this.exportSettings.includeOrigin ? 'checked' : ''}>
                      <span class="text-sm">Country of Origin</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="hs-code" ${this.exportSettings.includeHsCode ? 'checked' : ''}>
                      <span class="text-sm">HS Code</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="eccn" ${this.exportSettings.includeEccn ? 'checked' : ''}>
                      <span class="text-sm">ECCN</span>
                    </label>
                  </div>
                </div>
                
                <!-- Export Type Specific -->
                <div class="ml-2 space-y-2">
                  <h5 class="text-xs font-medium text-gray-700 dark:text-gray-300">Export Type Specific</h5>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="quantity" ${this.exportSettings.includeQuantity ? 'checked' : ''}>
                      <span class="text-sm">Quantity (Bucket Export)</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input type="checkbox" class="mr-2 export-field" data-field="price" ${this.exportSettings.includePrice ? 'checked' : ''}>
                      <span class="text-sm">Price (Detail Export)</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
            <button id="restoreDefaultBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
              Restore Defaults
            </button>
            <button id="saveSettingsBtn" class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Save Settings
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Append modal to container
    this.container.insertAdjacentHTML('beforeend', modalHtml);
    
    // Set up event listeners
    const closeButton = this.container.querySelector('#closeSettingsBtn');
    const restoreDefaultBtn = this.container.querySelector('#restoreDefaultBtn');
    const saveSettingsBtn = this.container.querySelector('#saveSettingsBtn');
    const settingsRefreshBtn = this.container.querySelector('#settingsRefreshBtn');
    const settingsExportBtn = this.container.querySelector('#settingsExportBtn');
    
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        const modal = this.container.querySelector('#masterPartSettingsModal');
        if (modal) modal.remove();
      });
    }
    
    if (restoreDefaultBtn) {
      restoreDefaultBtn.addEventListener('click', () => {
        // Reset all checkboxes to default checked state
        const checkboxes = this.container.querySelectorAll('.export-field');
        checkboxes.forEach(checkbox => {
          checkbox.checked = true;
        });
      });
    }
    
    if (saveSettingsBtn) {
      saveSettingsBtn.addEventListener('click', () => {
        try {
          // Collect settings from checkboxes
          const settings = {
            includeBasic: this.container.querySelector('.export-field[data-field="basic"]').checked,
            includeAdditional: this.container.querySelector('.export-field[data-field="additional"]').checked,
            includeImage: this.container.querySelector('.export-field[data-field="image"]').checked,
            includeManufacturer: this.container.querySelector('.export-field[data-field="manufacturer"]').checked,
            includeOrigin: this.container.querySelector('.export-field[data-field="origin"]').checked,
            includeHsCode: this.container.querySelector('.export-field[data-field="hs-code"]').checked,
            includeEccn: this.container.querySelector('.export-field[data-field="eccn"]').checked,
            includeQuantity: this.container.querySelector('.export-field[data-field="quantity"]').checked,
            includePrice: this.container.querySelector('.export-field[data-field="price"]').checked
          };
          
          // Update instance settings
          this.exportSettings = settings;
          
          // Save to storage
          this.saveExportSettingsToStorage();
          
          // Show confirmation
          this.addNotification('Settings saved successfully', 'success');
          
          // Close modal
          const modal = this.container.querySelector('#masterPartSettingsModal');
          if (modal) modal.remove();
        } catch (error) {
          console.error('Error saving settings:', error);
          this.addNotification('Error saving settings: ' + error.message, 'error');
        }
      });
    }
    
    // Add refresh button functionality
    if (settingsRefreshBtn) {
      settingsRefreshBtn.addEventListener('click', () => {
        // Close the modal first
        const modal = this.container.querySelector('#masterPartSettingsModal');
        if (modal) modal.remove();
        
        // Refresh the database
        this.init();
      });
    }
    
    // Add export button functionality
    if (settingsExportBtn) {
      settingsExportBtn.addEventListener('click', () => {
        // Close the modal first
        const modal = this.container.querySelector('#masterPartSettingsModal');
        if (modal) modal.remove();
        
        // Export the data
        this.exportPartsAsHtml(this.filteredParts, 'database', this.exportSettings);
      });
    }
    
    // Close when clicking outside
    const modal = this.container.querySelector('#masterPartSettingsModal');
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });
    }
  }
  
  async loadPartsData() {
    try {
      console.log("Loading parts data from masterparts.json...");
      this.showLoading();
      
      // First try: Load from chrome.storage
      if (chrome?.storage?.local) {
        try {
          const result = await new Promise(resolve => {
            chrome.storage.local.get('masterparts', data => {
              resolve(data?.masterparts || null);
            });
          });
          
          if (result && Array.isArray(result) && result.length > 0) {
            console.log(`Successfully loaded ${result.length} parts from chrome.storage`);
            this.partsData = result;
            this.filteredParts = [...result];
            this.isDataLoaded = true;
            this.addNotification(`Loaded ${result.length} parts from database`, "success");
            return true;
          }
        } catch (err) {
          console.warn("Failed to load from chrome.storage:", err);
        }
      }
      
      // Second try: Direct access using web_accessible_resources
      const urls = [
        chrome.runtime.getURL('masterparts.json'),
        chrome.runtime.getURL('json/masterparts.json'),
        '/masterparts.json',
        '/json/masterparts.json' 
      ];
      
      let data = null;
      for (const url of urls) {
        try {
          if (!url) continue;
          console.log(`Trying to fetch from ${url}...`);
          const response = await fetch(url);
          if (response.ok) {
            data = await response.json();
            console.log(`Successfully loaded ${data.length} parts from ${url}`);
            break;
          }
        } catch (err) {
          console.warn(`Fetch error for ${url}:`, err);
        }
      }
      
      // If we have data, use it
      if (data && Array.isArray(data) && data.length > 0) {
        // Store in local storage for future use
        if (chrome?.storage?.local) {
          chrome.storage.local.set({ 'masterparts': data }, function() {
            console.log('Saved parts data to chrome.storage for future use');
          });
        }
        
        this.partsData = data;
        this.filteredParts = [...data];
        this.isDataLoaded = true;
        this.addNotification(`Loaded ${data.length} parts from database`, "success");
        return true;
      }
      
      // Last try: Use messaging to the background script 
      if (chrome?.runtime?.sendMessage) {
        try {
          data = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(
              { action: "loadMasterPartsData" },
              (response) => {
                if (chrome.runtime.lastError) {
                  reject(new Error(chrome.runtime.lastError.message));
                  return;
                }
                
                if (response && response.success && response.data) {
                  resolve(response.data);
                } else {
                  reject(new Error('Invalid response from background script'));
                }
              }
            );
            
            // Set a timeout in case the background script doesn't respond
            setTimeout(() => {
              reject(new Error('Background script timeout'));
            }, 5000);
          });
          
          if (data && Array.isArray(data) && data.length > 0) {
            console.log(`Successfully loaded ${data.length} parts from background script`);
            this.partsData = data;
            this.filteredParts = [...data];
            this.isDataLoaded = true;
            this.addNotification(`Loaded ${data.length} parts from database`, "success");
            return true;
          }
        } catch (err) {
          console.warn("Error getting data from background script:", err);
        }
      }
      
      // If we reach here, all methods failed
      throw new Error('Failed to load data from any source. Check network connection and file paths.');
    } catch (error) {
      console.error('Error loading parts data:', error);
      this.addNotification(`Database error: ${error.message}`, "error");
      return false;
    }
  }

  showLoading() {
    const contentArea = this.container.querySelector('#partsContent');
    if (contentArea) {
      contentArea.innerHTML = `
        <div class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      `;
    }
  }

  showError(message) {
    const contentArea = this.container.querySelector('#partsContent');
    if (contentArea) {
      contentArea.innerHTML = `
        <div class="flex items-center justify-center h-64">
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative max-w-md">
            <div class="flex">
              <div class="py-1 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div>
                <p class="font-bold">Error</p>
                <p class="text-sm">${message}</p>
              </div>
            </div>
            <div class="mt-3 flex justify-center">
              <button id="retryBtn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
                Retry
              </button>
            </div>
          </div>
        </div>
      `;
      
      const retryBtn = this.container.querySelector('#retryBtn');
      if (retryBtn) {
        retryBtn.addEventListener('click', () => this.init());
      }
    }
  }

  render() {
    console.log("Rendering master parts component");

    // Set the title based on the active tab
    let title = 'Master Parts';
    if (this.activeTab === 'inventory-sync') {
      title = 'Inventory Sync';
    } else if (this.activeTab === 'vendor-metrics') {
      title = 'Vendor Metrics';
    } else if (this.activeTab === 'purchase-log') {
      title = 'Purchase Log';
    }

    // Create the HTML
    this.container.innerHTML = `
      <div class="p-2 dark:bg-gray-900">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-white">${title}</h2>
          <div class="flex space-x-1">
            <button class="px-2 py-1 text-xs font-medium rounded-md ${this.activeTab === 'master-parts' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'}" data-tab="master-parts">Master Parts</button>
            <button class="px-2 py-1 text-xs font-medium rounded-md ${this.activeTab === 'inventory-sync' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'}" data-tab="inventory-sync">Inventory Sync</button>
            <button class="px-2 py-1 text-xs font-medium rounded-md ${this.activeTab === 'vendor-metrics' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'}" data-tab="vendor-metrics">Vendor Metrics</button>
            <button class="px-2 py-1 text-xs font-medium rounded-md ${this.activeTab === 'purchase-log' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'}" data-tab="purchase-log">Purchase Log</button>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
          ${this.activeTab === 'master-parts' ? this.renderMasterPartsContent() : '<div id="tabContentContainer"></div>'}
        </div>
      </div>
    `;

    // Setup tab functionality
    this.setupTabFunctionality();

    if (this.activeTab === 'master-parts') {
      this.renderPartsTable();
      this.updatePagination();
    }
  }

  // Add a method to update the header title based on the active tab
  updateHeaderTitle() {
    const header = this.container.querySelector('h2');
    if (header) {
      if (this.activeTab === 'master-parts') {
        header.textContent = 'Master Parts';
      } else if (this.activeTab === 'inventory-sync') {
        header.textContent = 'Inventory Sync';
      } else if (this.activeTab === 'vendor-metrics') {
        header.textContent = 'Vendor Metrics';
      } else if (this.activeTab === 'purchase-log') {
        header.textContent = 'Purchase Log';
      }
    }
  }

  // Fix the switchTab method to properly handle tab activation
  async switchTab(tabId) {
    console.log(`Switching to tab: ${tabId}`);
    
    // Set new active tab
    this.activeTab = tabId;
    
    // Force a complete re-render of the container when switching to master-parts
    if (tabId === 'master-parts') {
      this.render();
      this.setupEventListeners();
      this.applySearch();
      this.updateSelectedPartsCounter();
      return;
    }
    
    try {
      // Purchase Log tab is accessible to all users without restriction
      const needsPermissionCheck = tabId !== 'purchase-log';
      
      // Check if user has permission to access this tab (except for purchase-log)
      let hasPermission = true;
      if (needsPermissionCheck) {
        hasPermission = await this.checkTabPermission(tabId);
        console.log(`Permission check result for ${tabId}: ${hasPermission}`);
        
        if (!hasPermission) {
          console.log(`Permission denied for tab: ${tabId}`);
          this.showPermissionDenied(tabId === 'inventory-sync' ? 'Inventory Sync' : 'Vendor Metrics');
          this.addNotification("You need admin privileges to access this section.", "warning");
          return;
        }
      }
      
      // Re-render with new active tab
      this.render();
      
      // Make sure tab buttons reflect the current active tab
      this.updateTabButtonStyles();
      
      // Update the header title to match the active tab
      this.updateHeaderTitle();
      
      // Initialize the appropriate component
      if (tabId === 'inventory-sync') {
        console.log("Initializing inventory-sync tab");
        if (!this.inventorySyncComponent) {
          this.addNotification("Error: Inventory Sync component not found", "error");
          return;
        }
        await this.inventorySyncComponent.init();
      } else if (tabId === 'vendor-metrics') {
        console.log("Initializing vendor-metrics tab");
        if (!this.vendorMetricsComponent) {
          this.addNotification("Error: Vendor Metrics component not found", "error");
          return;
        }
        await this.vendorMetricsComponent.init();
      } else if (tabId === 'purchase-log') {
        console.log("Initializing purchase-log tab");
        if (!this.purchaseOrderBarcodeComponent) {
          this.addNotification("Error: Purchase Log component not found", "error");
          return;
        }
        await this.purchaseOrderBarcodeComponent.init();
      }
      
      // Always setup tab functionality after any render
      this.setupTabFunctionality();
      
      console.log(`Tab switch to ${tabId} completed`);
    } catch (error) {
      console.error('Error during tab switch:', error);
      this.addNotification(`Error switching tabs: ${error.message}`, "error");
    }
  }

  // Calculate total pages
  getTotalPages() {
    return Math.ceil(this.filteredParts.length / this.itemsPerPage);
  }

  handleDarkMode() {
    const isDarkMode = document.body.classList.contains("dark-mode");
    const title = this.container.querySelector("h2");
    if (title) {
      title.style.color = isDarkMode ? "white" : "";
    }
  }

  renderPartsTable() {
    if (!this.isDataLoaded) {
      return `
        <div class="text-center py-12 text-gray-500 dark:text-gray-400">
          <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
          </svg>
          <p class="text-lg">Loading parts database...</p>
          <p class="text-sm">Please wait while we connect to the parts database.</p>
        </div>
      `;
    }

    if (this.filteredParts.length === 0) {
      return `
        <div class="text-center py-12 text-gray-500 dark:text-gray-400">
          <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p class="text-lg">No parts found</p>
          <p class="text-sm">Try adjusting your search criteria.</p>
        </div>
      `;
    }

    // Get page data
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredParts.length);
    const pageItems = this.filteredParts.slice(start, end);

    // Create table header row - Removed Supplier column to avoid horizontal scroll
    const headers = [
      { id: 'Part Number', label: 'Part #', width: '15%' },
      { id: 'Description', label: 'Description', width: '45%' },
      { id: 'Manufacturer', label: 'Manufacturer', width: '20%' },
      { id: 'Stock Status', label: 'Status', width: '12%' },
      { id: 'Default Price', label: 'Price', width: '8%' }
    ];

    return `
      <div>
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
            <tr>
              ${headers.map(header => {
                const isSorted = this.currentSort.field === header.id;
                const sortDirection = isSorted ? this.currentSort.direction : '';
                return `
                  <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer sort-header" 
                      data-field="${header.id}" style="width: ${header.width}">
                    <div class="flex items-center">
                      ${header.label}
                      ${isSorted ? 
                        `<svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          ${sortDirection === 'asc' ? 
                            `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>` : 
                            `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>`}
                         </svg>` : ''}
                    </div>
                  </th>
                `;
              }).join('')}
              <th scope="col" class="px-2 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider" style="width: 10%">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800">
            ${pageItems.map((part, idx) => {
              const isSelected = this.selectedParts.some(p => p['Part Number'] === part['Part Number']);
              return `
                <tr class="${idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'} hover:bg-blue-50 dark:hover:bg-blue-900 cursor-pointer ${isSelected ? 'bg-blue-50 dark:bg-blue-900 border-l-4 border-blue-500' : ''}" data-part-id="${part['Part Number']}">
                  <td class="px-2 py-1 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-xs font-medium text-blue-600 dark:text-blue-400">${part['Part Number'] || '-'}</div>
                      ${part._isCustomPart ? '<span class="ml-1 px-1 py-0.5 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded" title="Custom Part">C</span>' : ''}
                    </div>
                  </td>
                  <td class="px-2 py-1">
                    <div class="text-xs text-gray-900 dark:text-gray-300 truncate" style="max-width: 200px;" title="${part['Description'] || '-'}">${part['Description'] || '-'}</div>
                  </td>
                  <td class="px-2 py-1">
                    <div class="text-xs text-gray-900 dark:text-gray-300 truncate" style="max-width: 100px;" title="${part['Manufacturer'] || '-'}">${part['Manufacturer'] || '-'}</div>
                  </td>
                  <td class="px-2 py-1">
                    <span class="px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${this.getStockStatusClass(part['Stock Status'])}">${part['Stock Status'] || 'Unknown'}</span>
                  </td>
                  <td class="px-2 py-1">
                    <div class="text-xs text-gray-900 dark:text-gray-300">${part['Default Price'] ? '$' + part['Default Price'] : '-'}</div>
                  </td>
                  <td class="px-2 py-1 text-right">
                    <div class="flex space-x-1 justify-end">
                      ${isSelected ? 
                        `<button class="remove-selection-btn text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-1" data-part-id="${part['Part Number']}">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                        </button>` : 
                        `<button class="add-selection-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 p-1" data-part-id="${part['Part Number']}">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                        </button>`
                      }
                      <button class="view-part-btn text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 p-1" data-part-id="${part['Part Number']}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  getStockStatusClass(status) {
    switch (status) {
      case 'Stock':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 whitespace-nowrap';
      case 'Low Stock':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 whitespace-nowrap';
      case 'Out of Stock':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 whitespace-nowrap';
      case 'Discontinued':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 whitespace-nowrap';
      case 'Non Stock':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 whitespace-nowrap';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 whitespace-nowrap';
    }
  }

  setupEventListeners() {
    // Selected parts badge (bucket)
    const selectedPartsBadge = this.container.querySelector('#selectedPartsBadge');
    if (selectedPartsBadge) {
      selectedPartsBadge.addEventListener('click', (e) => {
        e.preventDefault();
        this.showBucketList();
      });
    }
    
    // Search input
    const searchInput = this.container.querySelector('#searchInput');
    if (searchInput) {
      searchInput.value = this.searchTerm;
      const debouncedSearch = this.debounce(() => {
        this.searchTerm = searchInput.value;
        this.updateClearButton();
        this.applySearch();
      }, 300); // 300ms debounce
      
      searchInput.addEventListener('input', debouncedSearch);
      
      // Add custom focus style for search input
      const style = document.createElement('style');
      style.id = 'searchInputFocusStyle';
      style.textContent = `
        #searchInput:focus {
          border-color: #3b82f6 !important; /* blue-500 */
          border-width: 1px !important;
          outline: none !important;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important; /* light blue shadow */
        }
      `;
      
      // Add style to document if it doesn't exist
      const existingStyle = document.getElementById('searchInputFocusStyle');
      if (!existingStyle) {
        document.head.appendChild(style);
      }
    }

    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clearSearchBtn');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.updateClearButton();
        this.applySearch();
      });
    }

    // Filter field
    const filterField = this.container.querySelector('#filterField');
    if (filterField) {
      filterField.value = this.selectedFilter;
      filterField.addEventListener('change', () => {
        this.selectedFilter = filterField.value;
        this.applySearch();
      });
    }

    // Add Part button
    const addPartBtn = this.container.querySelector('#addPartBtn');
    if (addPartBtn) {
      addPartBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.showAddPartModal();
      });
    }

    // Settings button
    const settingsBtn = this.container.querySelector('#settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.showMasterPartSettings();
      });
    }

    // Pagination buttons
    const prevPageBtn = this.container.querySelector('#prevPageBtn');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (this.currentPage > 1) {
          this.currentPage--;
          this.updatePartsTable();
        }
      });
    }

    const nextPageBtn = this.container.querySelector('#nextPageBtn');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (this.currentPage < this.getTotalPages()) {
          this.currentPage++;
          this.updatePartsTable();
        }
      });
    }

    // Page number buttons
    const pageButtons = this.container.querySelectorAll('.page-btn');
    pageButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const page = parseInt(button.getAttribute('data-page'));
        if (page !== this.currentPage) {
          this.currentPage = page;
          this.updatePartsTable();
        }
      });
    });

    // Sort headers
    const sortHeaders = this.container.querySelectorAll('.sort-header');
    sortHeaders.forEach(header => {
      header.addEventListener('click', (e) => {
        e.preventDefault();
        const field = header.getAttribute('data-field');
        this.sortTable(field);
      });
    });
    
    // Add to bucket buttons
    const addSelectionBtns = this.container.querySelectorAll('.add-selection-btn');
    addSelectionBtns.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation(); // Prevent row click
        const partId = button.getAttribute('data-part-id');
        this.addPartToBucket(partId);
      });
    });
    
    // Remove from bucket buttons
    const removeSelectionBtns = this.container.querySelectorAll('.remove-selection-btn');
    removeSelectionBtns.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation(); // Prevent row click
        const partId = button.getAttribute('data-part-id');
        this.removePartFromBucket(partId);
      });
    });

    // View part buttons
    const viewButtons = this.container.querySelectorAll('.view-part-btn');
    viewButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation(); // Prevent row click
        const partId = button.getAttribute('data-part-id');
        this.showPartDetails(partId);
      });
    });

    // Make rows clickable
    const rows = this.container.querySelectorAll('tbody tr');
    rows.forEach(row => {
      row.addEventListener('click', (e) => {
        e.preventDefault();
        const partId = row.getAttribute('data-part-id');
        if (partId) this.showPartDetails(partId);
      });
    });

    // Modal close buttons
    const closeModalBtn = this.container.querySelector('#closeModalBtn');
    const closeDetailBtn = this.container.querySelector('#closeDetailBtn');
    
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.hidePartDetails();
      });
    }
    
    if (closeDetailBtn) {
      closeDetailBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.hidePartDetails();
      });
    }

    // Export part button in detail view
    const exportPartBtn = this.container.querySelector('#exportPartBtn');
    if (exportPartBtn) {
      exportPartBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const partId = this.currentDetailPartId;
        if (partId) {
          const part = this.partsData.find(p => p['Part Number'] === partId);
          if (part) {
            // Always use the global export settings for consistency
            this.exportPartsAsHtml([part], 'detail', this.exportSettings);
          }
        }
      });
    }
    
    // Add to bucket button in detail view
    const addToBucketBtn = this.container.querySelector('#addToBucketBtn');
    if (addToBucketBtn) {
      addToBucketBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const partId = this.currentDetailPartId;
        if (partId) {
          const isSelected = this.selectedParts.some(p => p['Part Number'] === partId);
          if (isSelected) {
            this.removePartFromBucket(partId);
            addToBucketBtn.innerHTML = `
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Add to Bucket
            `;
          } else {
            this.addPartToBucket(partId);
            addToBucketBtn.innerHTML = `
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              Remove from Bucket
            `;
          }
        }
      });
    }
    
    // Edit part button
    const editPartBtn = this.container.querySelector('#editPartBtn');
    if (editPartBtn) {
      editPartBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.enablePartEditing();
      });
    }

    // Handle Escape key for modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hidePartDetails();
        this.hideAddPartModal();
        
        // Also close bucket modal if open
        const bucketModal = this.container.querySelector('#selectedPartsModal');
        if (bucketModal) bucketModal.remove();
      }
    });

    // Add event listener for dark mode changes
    document.addEventListener("darkModeChanged", () => this.handleDarkMode());
    
    // Setup tab functionality - this ensures tabs are clickable
    this.setupTabFunctionality();
  }
  
  // Enable part editing mode
  enablePartEditing() {
    const contentArea = this.container.querySelector('#partDetailContent');
    if (!contentArea) return;
    
    const partId = this.currentDetailPartId;
    if (!partId) return;
    
    const part = this.partsData.find(p => p['Part Number'] === partId);
    if (!part) return;
    
    // Create editable form view
    const editableHTML = `
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Basic Information</h4>
          <div class="border rounded-md overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Part Number</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Part Number'] || ''}" data-field="Part Number" readonly>
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Description</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Description'] || ''}" data-field="Description">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Supplier</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Supplier'] || ''}" data-field="Supplier">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Supplier P/N</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Supplier P/N'] || ''}" data-field="Supplier P/N">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Manufacturer</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Manufacturer'] || ''}" data-field="Manufacturer">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Country of Origin</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Country of Origin'] || ''}" data-field="Country of Origin">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">UOM</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['UOM'] || ''}" data-field="UOM">
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <div>
          <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Additional Details</h4>
          <div class="border rounded-md overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Stock Status</td>
                  <td class="px-2 py-1">
                    <select class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      data-field="Stock Status">
                      <option value="Stock" ${part['Stock Status'] === 'Stock' ? 'selected' : ''}>Stock</option>
                      <option value="Low Stock" ${part['Stock Status'] === 'Low Stock' ? 'selected' : ''}>Low Stock</option>
                      <option value="Out of Stock" ${part['Stock Status'] === 'Out of Stock' ? 'selected' : ''}>Out of Stock</option>
                      <option value="Non Stock" ${part['Stock Status'] === 'Non Stock' ? 'selected' : ''}>Non Stock</option>
                      <option value="Discontinued" ${part['Stock Status'] === 'Discontinued' ? 'selected' : ''}>Discontinued</option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Default Price</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Default Price'] || ''}" data-field="Default Price">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Weight</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Weight'] || ''}" data-field="Weight">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">HS Code</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['HS Code'] || ''}" data-field="HS Code">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">ECCN</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['ECCN'] || ''}" data-field="ECCN">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">USMCA/CASMA</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['USMCA/CASMA'] || ''}" data-field="USMCA/CASMA">
                  </td>
                </tr>
                <tr>
                  <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Schedule B Code</td>
                  <td class="px-2 py-1">
                    <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                      value="${part['Schedule B Code'] || ''}" data-field="Schedule B Code">
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div class="mt-3">
        <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Additional Information</h4>
        <div class="border rounded-md overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              <tr>
                <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Manufacturer & Address</td>
                <td class="px-2 py-1">
                  <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                    value="${part['Manufacturer & Address'] || ''}" data-field="Manufacturer & Address">
                </td>
              </tr>
              <tr>
                <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Customs Description</td>
                <td class="px-2 py-1">
                  <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                    value="${part['Customs Description'] || ''}" data-field="Customs Description">
                </td>
              </tr>
              <tr>
                <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Preference Criteria</td>
                <td class="px-2 py-1">
                  <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                    value="${part['Preference Criteria'] || ''}" data-field="Preference Criteria">
                </td>
              </tr>
              <tr>
                <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">HazMat Classification</td>
                <td class="px-2 py-1">
                  <input type="text" class="text-xs w-full border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 dark:bg-gray-700 dark:text-white" 
                    value="${part['HazMat Classification'] || ''}" data-field="HazMat Classification">
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div class="mt-4 flex justify-end">
        <button id="savePartBtn" class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center mr-2">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Save Changes
        </button>
        <button id="cancelEditBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
          Cancel
        </button>
      </div>
    `;
    
    contentArea.innerHTML = editableHTML;
    
    // Setup save and cancel buttons
    const saveBtn = contentArea.querySelector('#savePartBtn');
    const cancelBtn = contentArea.querySelector('#cancelEditBtn');
    
    if (saveBtn) {
      saveBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.savePartChanges(partId);
      });
    }
    
    if (cancelBtn) {
      cancelBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.showPartDetails(partId);
      });
    }
    
    // Change edit button to cancel
    const editBtn = this.container.querySelector('#editPartBtn');
    if (editBtn) {
      editBtn.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      `;
      
      // Replace event listener
      const newEditBtn = editBtn.cloneNode(true);
      editBtn.parentNode.replaceChild(newEditBtn, editBtn);
      
      newEditBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.showPartDetails(partId);
      });
    }
  }

  // Update pagination controls
  updatePagination() {
    const totalPages = this.getTotalPages();
    const pagination = this.container.querySelector('#pagination');
    const showingStart = this.container.querySelector('#showingStart');
    const showingEnd = this.container.querySelector('#showingEnd');
    const totalItems = this.container.querySelector('#totalItems');
    
    if (pagination) {
      let html = '';
      
      // Determine range of pages to show
      let startPage = Math.max(1, this.currentPage - 2);
      let endPage = Math.min(totalPages, startPage + 4);
      
      // Adjust if we're near the end
      if (endPage - startPage < 4 && startPage > 1) {
        startPage = Math.max(1, endPage - 4);
      }
      
      // First page
      if (startPage > 1) {
        html += `
          <button class="page-btn px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="1">1</button>
        `;
        
        if (startPage > 2) {
          html += `
            <span class="px-2 py-1 text-gray-500 dark:text-gray-400">...</span>
          `;
        }
      }
      
      // Page numbers
      for (let i = startPage; i <= endPage; i++) {
        html += `
          <button class="page-btn px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md ${i === this.currentPage ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}" data-page="${i}">${i}</button>
        `;
      }
      
      // Last page
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          html += `
            <span class="px-2 py-1 text-gray-500 dark:text-gray-400">...</span>
          `;
        }
        
        html += `
          <button class="page-btn px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="${totalPages}">${totalPages}</button>
        `;
      }
      
      pagination.innerHTML = html;
      
      // Add event listeners
      const pageButtons = pagination.querySelectorAll('.page-btn');
      pageButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const page = parseInt(button.getAttribute('data-page'));
          if (page !== this.currentPage) {
            this.currentPage = page;
            this.updatePartsTable();
          }
        });
      });
    }
    
    // Update showing text
    if (showingStart && showingEnd && totalItems) {
      const start = this.filteredParts.length > 0 ? 
        (this.currentPage - 1) * this.itemsPerPage + 1 : 0;
      const end = Math.min(start + this.itemsPerPage - 1, this.filteredParts.length);
      
      showingStart.textContent = start;
      showingEnd.textContent = end;
      totalItems.textContent = this.filteredParts.length;
    }
    
    // Update prev/next buttons
    const prevBtn = this.container.querySelector('#prevPageBtn');
    const nextBtn = this.container.querySelector('#nextPageBtn');
    
    if (prevBtn) {
      if (this.currentPage <= 1) {
        prevBtn.classList.add('opacity-50', 'cursor-not-allowed');
        prevBtn.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      } else {
        prevBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        prevBtn.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      }
    }
    
    if (nextBtn) {
      if (this.currentPage >= totalPages) {
        nextBtn.classList.add('opacity-50', 'cursor-not-allowed');
        nextBtn.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      } else {
        nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        nextBtn.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      }
    }
  }

  // Update the parts table when pagination, sorting, or filtering changes
  updatePartsTable() {
    const partsContent = this.container.querySelector('#partsContent');
    if (partsContent) {
      partsContent.innerHTML = this.renderPartsTable();
      this.setupEventListeners();
      this.updatePagination();
    }
  }

  // Apply search to filter parts
  applySearch() {
    this.filteredParts = this.partsData.filter(part => {
      if (!this.searchTerm) return true;
      
      const searchLower = this.searchTerm.toLowerCase();
      
      if (this.selectedFilter === 'all') {
        return Object.values(part).some(value => 
          value && value.toString().toLowerCase().includes(searchLower)
        );
      } else {
        return part[this.selectedFilter] && 
          part[this.selectedFilter].toString().toLowerCase().includes(searchLower);
      }
    });
    
    // Reset to first page when search changes
    this.currentPage = 1;
    this.updatePartsTable();
  }

  // Update the clear search button visibility
  updateClearButton() {
    const clearBtn = this.container.querySelector('#clearSearchBtn');
    if (clearBtn) {
      if (this.searchTerm) {
        clearBtn.classList.remove('hidden');
      } else {
        clearBtn.classList.add('hidden');
      }
    }
  }

  // Sort table by field
  sortTable(field) {
    if (this.currentSort.field === field) {
      // Toggle direction if same field
      this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
      // New field, default to ascending
      this.currentSort.field = field;
      this.currentSort.direction = 'asc';
    }
    
    this.filteredParts.sort((a, b) => {
      const aVal = a[field] ? a[field].toString() : '';
      const bVal = b[field] ? b[field].toString() : '';
      
      // Handle numeric values differently
      if (field === 'Default Price' && a[field] && b[field]) {
        return this.currentSort.direction === 'asc' ? 
          parseFloat(a[field]) - parseFloat(b[field]) : 
          parseFloat(b[field]) - parseFloat(a[field]);
      }
      
      return this.currentSort.direction === 'asc' ? 
        aVal.localeCompare(bVal) : 
        bVal.localeCompare(aVal);
    });
    
    this.updatePartsTable();
  }

  // Show part details modal
  async showPartDetails(partId) {
    const part = this.partsData.find(p => p['Part Number'] === partId);
    if (!part) return;
    
    // Store current part ID
    this.currentDetailPartId = partId;
    
    // Update modal title
    const modalTitle = this.container.querySelector('#modalTitle');
    if (modalTitle) {
      modalTitle.textContent = `Part Details: ${partId}`;
    }
    
    // Check if part is in bucket to update button text
    const isSelected = this.selectedParts.some(p => p['Part Number'] === partId);
    const addToBucketBtn = this.container.querySelector('#addToBucketBtn');
    if (addToBucketBtn) {
      if (isSelected) {
        addToBucketBtn.innerHTML = `
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Remove from Bucket
        `;
      } else {
        addToBucketBtn.innerHTML = `
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Add to Bucket
        `;
      }
    }
    
    // Update part details content
    const contentArea = this.container.querySelector('#partDetailContent');
    if (contentArea) {
      contentArea.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Basic Information</h4>
            <div class="border rounded-md overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Part Number</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Part Number'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Description</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Description'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Supplier</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Supplier'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Supplier P/N</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Supplier P/N'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Manufacturer</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Manufacturer'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Country of Origin</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Country of Origin'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">UOM</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['UOM'] || '-'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div>
            <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Additional Details</h4>
            <div class="border rounded-md overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Stock Status</td>
                    <td class="px-2 py-1">
                      <span class="px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${this.getStockStatusClass(part['Stock Status'])}">${part['Stock Status'] || 'Unknown'}</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Default Price</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Default Price'] ? '$' + part['Default Price'] : '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Weight</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Weight'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">HS Code</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['HS Code'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">ECCN</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['ECCN'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">USMCA/CASMA</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['USMCA/CASMA'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Schedule B Code</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Schedule B Code'] || '-'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
          <div id="partImageContainer">
            <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Part Image</h4>
            <div class="border rounded-md p-3 flex items-center justify-center" style="height: 200px; width: 100%;">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          </div>
          
          <div>
            <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Additional Information</h4>
            <div class="border rounded-md overflow-hidden" style="max-height: 200px; overflow-y: auto;">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Manufacturer & Address</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Manufacturer & Address'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Customs Description</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Customs Description'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Preference Criteria</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['Preference Criteria'] || '-'}</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">HazMat Classification</td>
                    <td class="px-2 py-1 text-xs text-gray-900 dark:text-gray-300">${part['HazMat Classification'] || '-'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      `;
      
      // Load image
      const imageContainer = this.container.querySelector('#partImageContainer');
      if (imageContainer) {
        const imageDiv = imageContainer.querySelector('div');
        if (imageDiv) {
          this.loadPartImage(partId, imageDiv);
        }
      }
    }
    
    // Show modal
    const modal = this.container.querySelector('#partDetailModal');
    if (modal) {
      modal.classList.remove('hidden');
    }
  }

  // Hide part details modal
  hidePartDetails() {
    const modal = this.container.querySelector('#partDetailModal');
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  // Add notification
  addNotification(message, type = 'info') {
    this.notificationSystem.addNotification(message, type);
  }

  // Save part changes
  savePartChanges(partId) {
    const part = this.partsData.find(p => p['Part Number'] === partId);
    if (!part) return;
    
    // Collect all input values
    const inputs = this.container.querySelectorAll('#partDetailContent input, #partDetailContent select');
    inputs.forEach(input => {
      const field = input.getAttribute('data-field');
      if (field) {
        part[field] = input.value;
      }
    });
    
    // Update filtered parts if needed
    this.applySearch();
    
    // Update selected parts
    const selectedPartIndex = this.selectedParts.findIndex(p => p['Part Number'] === partId);
    if (selectedPartIndex !== -1) {
      // Create a new object to update the part
      const updatedPart = { ...this.selectedParts[selectedPartIndex] };
      
      // Copy values from the edited part (except quantity)
      Object.keys(part).forEach(key => {
        if (key !== 'quantity') {
          updatedPart[key] = part[key];
        }
      });
      
      // Replace the part in the selected parts array
      this.selectedParts[selectedPartIndex] = updatedPart;
      
      // Save to storage
      this.saveSelectedPartsToStorage();
    }
    
    // Show the details view again
    this.showPartDetails(partId);
    
    // Show notification
    this.addNotification(`Part ${partId} updated successfully`, 'success');
    
    // Store in chrome storage if available
    if (chrome?.storage?.local) {
      chrome.storage.local.set({ 'masterparts': this.partsData }, function() {
        console.log('Saved updated parts data to chrome.storage');
      });
    }
  }

  // Display a message about missing cookie permissions
  showCookiePermissionsMessage() {
    // Skip showing cookie permission warnings entirely
    return;
  }

  // Show permission denied message for restricted tabs
  showPermissionDenied(tabName) {
    this.container.innerHTML = `
      <div class="p-2 dark:bg-gray-900">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-white">${tabName}</h2>
          <div class="flex space-x-1">
            <button class="px-2 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600" data-tab="master-parts">Master Parts</button>
            <button class="px-2 py-1 text-xs font-medium rounded-md ${this.activeTab === 'inventory-sync' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'} opacity-50 cursor-not-allowed" title="Admin access required" data-tab="inventory-sync">Inventory Sync</button>
            <button class="px-2 py-1 text-xs font-medium rounded-md ${this.activeTab === 'vendor-metrics' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600'} opacity-50 cursor-not-allowed" title="Admin access required" data-tab="vendor-metrics">Vendor Metrics</button>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
          <div class="text-center py-12 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            <p class="text-lg text-blue-600 dark:text-blue-400">Permission Denied</p>
            <p class="text-sm mt-2">You need administrator privileges to access this section.</p>
            <button id="returnToMasterPartsBtn" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
              Return to Master Parts
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Set up return button
    const returnBtn = this.container.querySelector('#returnToMasterPartsBtn');
    if (returnBtn) {
      returnBtn.addEventListener('click', () => {
        this.switchTab('master-parts');
      });
    }
    
    // Set up tab functionality
    this.setupTabFunctionality();
    console.log("Tab functionality set up in permission denied screen");
  }

  // Check if user has permission to access a tab
  async checkTabPermission(tabId) {
    // Master Parts tab and Purchase Log tab are accessible to all
    if (tabId === 'master-parts' || tabId === 'purchase-log') return true;
    
    console.log(`Checking permissions for tab: ${tabId}`);
    
    // Return a Promise to properly handle the async chrome.storage check
    return new Promise((resolve) => {
      if (typeof chrome !== "undefined" && chrome.storage) {
        chrome.storage.local.get("user", (result) => {
          if (result && result.user && result.user.Role) {
            const isAdmin = result.user.Role === 'Admin' || result.user.Role === 'admin';
            console.log(`User role from chrome.storage: ${result.user.Role}, isAdmin: ${isAdmin}`);
            resolve(isAdmin);
          } else {
            console.log('No user role found in chrome.storage');
            resolve(false);
          }
        });
      } else {
        console.log('Chrome storage not available');
        resolve(false);
      }
    });
  }

  // Add a new method to update tab button styles
  updateTabButtonStyles() {
    console.log("Updating tab button styles");
    const tabButtons = this.container.querySelectorAll("button[data-tab]");
    tabButtons.forEach(button => {
      const tabId = button.getAttribute("data-tab");
      
      // Remove all styling
      button.classList.remove(
        "bg-blue-100", "text-blue-700", "dark:bg-blue-900", "dark:text-blue-200",
        "bg-gray-100", "text-gray-700", "dark:bg-gray-700", "dark:text-gray-200"
      );
      
      // Add appropriate styling
      if (tabId === this.activeTab) {
        button.classList.add("bg-blue-100", "text-blue-700", "dark:bg-blue-900", "dark:text-blue-200");
      } else {
        button.classList.add("bg-gray-100", "text-gray-700", "dark:bg-gray-700", "dark:text-gray-200");
        button.classList.add("hover:bg-gray-200", "dark:hover:bg-gray-600");
      }
    });
  }

  // Improve the setupTabFunctionality method
  setupTabFunctionality() {
    console.log("Setting up tab functionality");
    
    // Remove any existing tab event listeners to prevent duplicates
    const tabButtons = this.container.querySelectorAll("button[data-tab]");
    tabButtons.forEach(button => {
      const newButton = button.cloneNode(true);
      button.parentNode.replaceChild(newButton, button);
    });
    
    // Add fresh event listeners
    const freshTabButtons = this.container.querySelectorAll("button[data-tab]");
    freshTabButtons.forEach(button => {
      const tabId = button.getAttribute("data-tab");
      
      button.addEventListener("click", async (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // Don't do anything if clicking the current tab
        if (tabId === this.activeTab) return;
        
        console.log(`Tab button clicked: ${tabId}`);
        
        // Temporarily add active styling to clicked tab
        this.activeTab = tabId;
        this.updateTabButtonStyles();
        
        // Switch tab if permission allows
        const hasPermission = await this.checkTabPermission(tabId);
        if (tabId === 'master-parts' || hasPermission) {
          console.log(`Permission granted for tab: ${tabId}`);
          await this.switchTab(tabId);
        } else {
          console.log(`Permission denied for tab: ${tabId}`);
          this.showPermissionDenied(tabId === 'inventory-sync' ? 'Inventory Sync' : 'Vendor Metrics');
          this.addNotification("You need admin privileges to access this section.", "warning");
        }
      });
    });
    
    // Update tab styles based on current active tab
    this.updateTabButtonStyles();
    
    console.log("Tab event listeners setup complete");
  }

  // Add a method to render only the Master Parts content
  renderMasterPartsContent() {
    return `
      <!-- Search Bar -->
      <div class="mb-2 flex flex-wrap items-center">
        <div class="relative flex-grow mr-2">
          <input type="text" id="searchInput" class="w-full pl-9 pr-4 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white" placeholder="Search parts...">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <button id="clearSearchBtn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${this.searchTerm ? '' : 'hidden'}">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <select id="filterField" class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white mr-2">
          <option value="all">All Fields</option>
          <option value="Part Number">Part Number</option>
          <option value="Description">Description</option>
          <option value="Manufacturer">Manufacturer</option>
          <option value="Country of Origin">Country of Origin</option>
        </select>
        
        <!-- Add Part Button -->
        <button id="addPartBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;" title="Add New Part">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M12 4v16m8-8H4"></path>
          </svg>
        </button>

        <!-- Bucket Button -->
        <button id="selectedPartsBadge" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 relative text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
        </svg>
        ${this.selectedParts.length > 0 ? `<span id="selectedPartsCounter" class="absolute -top-2 -right-2 bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">${this.selectedParts.length}</span>` : ''}
      </button>
      
      <!-- Settings Button -->
      <button id="settingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
      </button>
    </div>
    
    <!-- Content Area -->
    <div id="partsContent" class="overflow-auto" style="max-height: 500px;">
      ${this.isLoading ? 
        `<div class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>` : 
        this.renderPartsTable()}
    </div>
    
    <!-- Pagination -->
    <div class="flex items-center justify-between mt-2 text-xs">
      <div class="text-gray-500 dark:text-gray-400">
        Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="totalItems">0</span> parts
      </div>
      <div class="flex space-x-1">
        <button id="prevPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <div id="pagination" class="flex space-x-1">
          <!-- Page numbers will be inserted here -->
        </div>
        <button id="nextPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage >= this.getTotalPages() ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Part Detail Modal -->
    <div id="partDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-3 w-11/12 max-h-[500px] overflow-y-auto">
        <div class="flex justify-between items-center mb-2 pb-1 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white" id="modalTitle">Part Details</h3>
          <div class="flex space-x-2">
            <button id="editPartBtn" class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </button>
            <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        <div id="partDetailContent" class="text-sm max-h-[400px] overflow-y-auto">
          <!-- Part details will be inserted here -->
        </div>
        <div class="flex justify-end mt-3 pt-2 border-t border-gray-200 dark:border-gray-700 space-x-2">
          <button id="addToBucketBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Add to Bucket
          </button>
          <button id="exportPartBtn" class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            Export
          </button>
          <button id="closeDetailBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
            Close
          </button>
        </div>
      </div>
    </div>
    
    <!-- Add Part Modal -->
    <div id="addPartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-3 w-11/12 max-h-[500px] overflow-y-auto">
        <div class="flex justify-between items-center mb-2 pb-1 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Add New Part</h3>
          <button id="closeAddPartModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="text-sm max-h-[400px] overflow-y-auto">
          <form id="addPartForm">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Basic Information</h4>
              <div class="border rounded-md overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Part Number *</td>
                      <td class="px-2 py-1">
                        <input type="text" name="partNumber" required class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Description *</td>
                      <td class="px-2 py-1">
                        <textarea name="description" required rows="2" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0 resize-none"></textarea>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Supplier</td>
                      <td class="px-2 py-1">
                        <input type="text" name="supplier" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Supplier P/N</td>
                      <td class="px-2 py-1">
                        <input type="text" name="supplierPN" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Manufacturer</td>
                      <td class="px-2 py-1">
                        <input type="text" name="manufacturer" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Country of Origin</td>
                      <td class="px-2 py-1">
                        <input type="text" name="countryOfOrigin" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">UOM</td>
                      <td class="px-2 py-1">
                        <input type="text" name="uom" placeholder="EA, PCS, etc." class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Additional Details</h4>
              <div class="border rounded-md overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Stock Status</td>
                      <td class="px-2 py-1">
                        <select name="stockStatus" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                          <option value="Stock">Stock</option>
                          <option value="Low Stock">Low Stock</option>
                          <option value="Out of Stock">Out of Stock</option>
                          <option value="Non Stock">Non Stock</option>
                          <option value="Discontinued">Discontinued</option>
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Default Price</td>
                      <td class="px-2 py-1">
                        <input type="number" name="defaultPrice" step="0.01" min="0" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Weight</td>
                      <td class="px-2 py-1">
                        <input type="text" name="weight" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">HS Code</td>
                      <td class="px-2 py-1">
                        <input type="text" name="hsCode" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">ECCN</td>
                      <td class="px-2 py-1">
                        <input type="text" name="eccn" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">USMCA/CASMA</td>
                      <td class="px-2 py-1">
                        <input type="text" name="usmcaCasma" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400">Schedule B Code</td>
                      <td class="px-2 py-1">
                        <input type="text" name="scheduleBCode" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Part Image</h4>
              <div class="border rounded-md p-3 flex items-center justify-center" style="height: 200px; width: 100%;">
                <div class="text-center h-full w-full flex flex-col items-center justify-center">
                  <div id="imagePreview" class="hidden">
                    <img id="previewImg" src="" alt="Preview" class="max-h-28 max-w-full object-contain border border-gray-200 dark:border-gray-700 rounded">
                    <p class="text-xs text-gray-500 dark:text-gray-400 truncate mt-2">Preview</p>
                  </div>
                  <div id="imageUploadArea" class="text-center">
                    <input type="file" name="partImage" accept="image/*" id="partImageInput" class="hidden">
                    <label for="partImageInput" class="cursor-pointer">
                      <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 hover:border-blue-500">
                        <svg class="w-8 h-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Click to upload image</p>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-semibold text-gray-800 dark:text-white mb-1">Additional Information</h4>
              <div class="border rounded-md overflow-hidden" style="max-height: 200px; overflow-y: auto;">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Manufacturer & Address</td>
                      <td class="px-2 py-1">
                        <textarea name="manufacturerAddress" rows="2" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0 resize-none"></textarea>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Customs Description</td>
                      <td class="px-2 py-1">
                        <textarea name="customsDescription" rows="2" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0 resize-none"></textarea>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">Preference Criteria</td>
                      <td class="px-2 py-1">
                        <textarea name="preferenceCriteria" rows="2" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0 resize-none"></textarea>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1 bg-gray-50 dark:bg-gray-800 text-xs font-medium text-gray-500 dark:text-gray-400 w-1/4">HazMat Classification</td>
                      <td class="px-2 py-1">
                        <input type="text" name="hazmatClassification" class="w-full border-0 bg-transparent text-xs text-gray-900 dark:text-gray-300 focus:outline-none focus:ring-0">
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          </form>
        </div>

        <div class="flex justify-end mt-3 pt-2 border-t border-gray-200 dark:border-gray-700 space-x-2">
          <button type="submit" form="addPartForm" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Add Part
          </button>
          <button type="button" id="cancelAddPartBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  `;
}
}